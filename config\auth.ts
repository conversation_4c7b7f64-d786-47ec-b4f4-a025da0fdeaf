/**
 * Configurações de autenticação
 * 
 * Para configurar a autenticação com Google:
 * 
 * 1. Acesse o Google Cloud Console: https://console.cloud.google.com/
 * 2. Crie um novo projeto ou selecione um existente
 * 3. Ative a Google+ API
 * 4. Vá para "Credenciais" > "Criar credenciais" > "ID do cliente OAuth 2.0"
 * 5. Configure as origens autorizadas:
 *    - Para desenvolvimento: http://localhost:19006
 *    - Para produção: sua URL de produção
 * 6. Configure os URIs de redirecionamento:
 *    - Para desenvolvimento: http://localhost:19006/auth
 *    - Para produção: sua URL de produção + /auth
 * 7. Copie o Client ID e cole abaixo
 */

export const AUTH_CONFIG = {
  // Substitua pelo seu Google Client ID
  GOOGLE_CLIENT_ID: process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID || 'YOUR_GOOGLE_CLIENT_ID_HERE',
  
  // Configurações do OAuth
  SCOPES: ['openid', 'profile', 'email'],
  
  // URLs de redirecionamento
  REDIRECT_URI: {
    development: 'http://localhost:19006/auth',
    production: 'https://your-app.com/auth', // Substitua pela sua URL
  },
  
  // Endpoints do Google
  ENDPOINTS: {
    authorization: 'https://accounts.google.com/o/oauth2/v2/auth',
    token: 'https://oauth2.googleapis.com/token',
    userInfo: 'https://www.googleapis.com/oauth2/v2/userinfo',
  },
};

/**
 * Instruções para configurar as variáveis de ambiente:
 * 
 * 1. Crie um arquivo .env na raiz do projeto
 * 2. Adicione a linha: EXPO_PUBLIC_GOOGLE_CLIENT_ID=seu_client_id_aqui
 * 3. Reinicie o servidor de desenvolvimento
 * 
 * Exemplo de .env:
 * EXPO_PUBLIC_GOOGLE_CLIENT_ID=*********-abcdefghijklmnop.apps.googleusercontent.com
 */
