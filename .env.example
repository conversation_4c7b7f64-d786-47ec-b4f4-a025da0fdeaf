# Configurações de Autenticação Google
# Copie este arquivo para .env e configure suas credenciais

# Google OAuth Client ID
# Obtenha em: https://console.cloud.google.com/
EXPO_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id_here.apps.googleusercontent.com

# Instruções para configurar:
# 1. Acesse https://console.cloud.google.com/
# 2. Crie um projeto ou selecione um existente
# 3. Ative a Google+ API
# 4. Vá para "Credenciais" > "Criar credenciais" > "ID do cliente OAuth 2.0"
# 5. Selecione "Aplicativo da Web"
# 6. Configure as origens autorizadas:
#    - http://localhost:19006 (desenvolvimento)
# 7. Configure os URIs de redirecionamento:
#    - http://localhost:19006/auth (desenvolvimento)
# 8. Copie o Client ID e cole acima
